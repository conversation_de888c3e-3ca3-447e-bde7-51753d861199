import { ChatOpenAI } from '@langchain/openai';
import { PromptTemplate } from '@langchain/core/prompts';
import { HumanMessage, AIMessage } from '@langchain/core/messages';
import { StateGraph, START, END } from '@langchain/langgraph';
import { config, defaultAgentConfigs } from '../config';
import { NodeExecutionResult, AgentError } from '../types';
import { logger, PerformanceLogger } from '../utils/logger';

// 定义Agent D的状态接口
interface AgentDState {
  // 输入数据
  original_input: string;
  agent_a_result: string;
  agent_b_result: string;
  agent_c_result: string;
  session_id: string;

  // 处理状态
  inputs_validated: boolean;
  inputs_preprocessed: boolean;
  aggregation_completed: boolean;
  response_enhanced: boolean;

  // 处理结果
  processed_inputs?: any;
  aggregated_result?: string;
  enhanced_result?: string;
  final_result?: string;

  // 元数据
  execution_time?: number;
  tokens_used?: number;
  quality_metrics?: any;
  execution_summary?: string;
  error?: string;

  metadata?: {
    input_agents_count?: number;
    output_length?: number;
    model_used?: string;
    complexity_analysis?: any;
  };
}

export class AgentD {
  private model: ChatOpenAI;
  private promptTemplate: PromptTemplate;
  private config: any;
  private workflow: any;

  constructor() {
    this.config = defaultAgentConfigs.agent_d;

    // 初始化 ChatOpenAI 模型
    this.model = new ChatOpenAI({
      openAIApiKey: config.openai.apiKey,
      modelName: this.config.model,
      temperature: this.config.temperature,
      maxTokens: this.config.maxTokens,
      timeout: 120000,
    });

    // 创建汇聚分析的提示词模板
    this.promptTemplate = PromptTemplate.fromTemplate(`
${this.config.systemPrompt}

你现在需要整合以下来自不同智能体的分析结果：

=== 原始用户输入 ===
{original_input}

=== Agent A 的分析结果 ===
{agent_a_result}

=== Agent B 的迭代分析结果 ===
{agent_b_result}

=== Agent C 的函数处理结果 ===
{agent_c_result}

=== 整合任务 ===
请基于以上所有信息，提供一个完整、准确、有价值的最终回答。你的整合需要：

1. **信息综合**: 将所有智能体的输出进行有机整合
2. **逻辑梳理**: 确保最终答案逻辑清晰、条理分明
3. **价值提取**: 突出最有价值的见解和结论
4. **完整性检查**: 确保回答完整地解决了用户的原始问题
5. **质量保证**: 提供高质量、专业的最终答案

=== 最终整合分析 ===
`);

    // 初始化 StateGraph 工作流
    this.workflow = this.createWorkflow();

    logger.info('Agent D initialized with StateGraph aggregation capability', {
      model: this.config.model,
      temperature: this.config.temperature
    });
  }

  private createWorkflow(): any {
    try {
      // 定义状态配置 - 使用channels包装
      const stateConfig = {
        channels: {
          original_input: {
            value: (x: any, y: any) => y ?? x ?? "",
            default: () => "",
          },
          agent_a_result: {
            value: (x: any, y: any) => y ?? x ?? "",
            default: () => "",
          },
          agent_b_result: {
            value: (x: any, y: any) => y ?? x ?? "",
            default: () => "",
          },
          agent_c_result: {
            value: (x: any, y: any) => y ?? x ?? "",
            default: () => "",
          },
          session_id: {
            value: (x: any, y: any) => y ?? x ?? "",
            default: () => "",
          },
          inputs_validated: {
            value: (x: any, y: any) => y ?? x ?? false,
            default: () => false,
          },
          inputs_preprocessed: {
            value: (x: any, y: any) => y ?? x ?? false,
            default: () => false,
          },
          aggregation_completed: {
            value: (x: any, y: any) => y ?? x ?? false,
            default: () => false,
          },
          response_enhanced: {
            value: (x: any, y: any) => y ?? x ?? false,
            default: () => false,
          },
          processed_inputs: {
            value: (x: any, y: any) => y ?? x ?? null,
            default: () => null,
          },
          aggregated_result: {
            value: (x: any, y: any) => y ?? x ?? "",
            default: () => "",
          },
          enhanced_result: {
            value: (x: any, y: any) => y ?? x ?? "",
            default: () => "",
          },
          final_result: {
            value: (x: any, y: any) => y ?? x ?? "",
            default: () => "",
          },
          execution_time: {
            value: (x: any, y: any) => y ?? x ?? 0,
            default: () => 0,
          },
          tokens_used: {
            value: (x: any, y: any) => y ?? x ?? 0,
            default: () => 0,
          },
          quality_metrics: {
            value: (x: any, y: any) => y ?? x ?? null,
            default: () => null,
          },
          execution_summary: {
            value: (x: any, y: any) => y ?? x ?? "",
            default: () => "",
          },
          error: {
            value: (x: any, y: any) => y ?? x ?? "",
            default: () => "",
          },
          metadata: {
            value: (x: any, y: any) => ({ ...x, ...y }),
            default: () => ({}),
          },
        }
      };

      // 创建StateGraph - 使用类型断言绕过复杂的类型检查
      const workflow = new StateGraph(stateConfig as any)
        .addNode("validate_inputs", this.validateInputsNode.bind(this) as any)
        .addNode("preprocess_inputs", this.preprocessInputsNode.bind(this) as any)
        .addNode("aggregate_results", this.aggregateResultsNode.bind(this) as any)
        .addNode("enhance_response", this.enhanceResponseNode.bind(this) as any)
        .addNode("finalize_result", this.finalizeResultNode.bind(this) as any)
        .addEdge(START, "validate_inputs")
        .addEdge("validate_inputs", "preprocess_inputs")
        .addEdge("preprocess_inputs", "aggregate_results")
        .addEdge("aggregate_results", "enhance_response")
        .addEdge("enhance_response", "finalize_result")
        .addEdge("finalize_result", END)
        .compile();

      logger.debug('LangGraph workflow initialized for Agent D');
      return workflow;

    } catch (error: any) {
      logger.error('Failed to initialize LangGraph workflow for Agent D', { error: error.message });
      throw new AgentError('Workflow initialization failed', 'WORKFLOW_INIT_FAILED', 'agent_d');
    }
  }

  async execute(combinedInput: any, sessionId: string): Promise<NodeExecutionResult> {
    try {
      logger.debug('Agent D starting StateGraph execution', {
        session_id: sessionId,
        has_original_input: !!combinedInput.original_input,
        has_agent_a_result: !!combinedInput.agent_a_result,
        has_agent_b_result: !!combinedInput.agent_b_result,
        has_agent_c_result: !!combinedInput.agent_c_result
      });

      // 使用LangGraph StateGraph执行
      const state: AgentDState = {
        original_input: combinedInput.original_input || "",
        agent_a_result: combinedInput.agent_a_result || "",
        agent_b_result: combinedInput.agent_b_result || "",
        agent_c_result: combinedInput.agent_c_result || "",
        session_id: sessionId,
        inputs_validated: false,
        inputs_preprocessed: false,
        aggregation_completed: false,
        response_enhanced: false
      };

      const result = await this.workflow.invoke(state);

      if (result.error) {
        throw new AgentError(result.error, 'WORKFLOW_EXECUTION_FAILED', 'agent_d');
      }

      return {
        success: true,
        result: result.final_result || '',
        execution_time: result.execution_time || 0,
        tokens_used: result.tokens_used || 0
      };

    } catch (error: any) {
      logger.error('Agent D StateGraph execution failed', {
        session_id: sessionId,
        error: error.message,
        stack: error.stack
      });

      if (error instanceof AgentError) {
        throw error;
      }

      throw new AgentError(
        `Agent D execution failed: ${error.message}`,
        'EXECUTION_FAILED',
        'agent_d',
        { originalError: error.message }
      );
    }
  }

  // StateGraph 节点函数
  private async validateInputsNode(state: AgentDState): Promise<Partial<AgentDState>> {
    try {
      logger.debug('Agent D validate inputs node', {
        session_id: state.session_id
      });

      // 验证所有必需的输入
      const requiredFields = ['original_input', 'agent_a_result', 'agent_b_result', 'agent_c_result'];
      const missingFields = requiredFields.filter(field => !state[field as keyof AgentDState]);

      if (missingFields.length > 0) {
        return {
          error: `Missing required inputs: ${missingFields.join(', ')}`,
          inputs_validated: false
        };
      }

      // 验证输入内容不为空
      for (const field of requiredFields) {
        const value = state[field as keyof AgentDState];
        if (typeof value === 'string' && value.trim().length === 0) {
          return {
            error: `Input field ${field} cannot be empty`,
            inputs_validated: false
          };
        }
      }

      return {
        inputs_validated: true
      };

    } catch (error: any) {
      logger.error('Validate inputs node failed', {
        session_id: state.session_id,
        error: error.message
      });

      return {
        error: `Input validation failed: ${error.message}`,
        inputs_validated: false
      };
    }
  }

  private async preprocessInputsNode(state: AgentDState): Promise<Partial<AgentDState>> {
    try {
      logger.debug('Agent D preprocess inputs node', {
        session_id: state.session_id
      });

      const processedInputs = {
        original_input: this.sanitizeInput(state.original_input),
        agent_a_result: this.sanitizeInput(state.agent_a_result),
        agent_b_result: this.sanitizeInput(state.agent_b_result),
        agent_c_result: this.sanitizeInput(state.agent_c_result)
      };

      return {
        processed_inputs: processedInputs,
        inputs_preprocessed: true
      };

    } catch (error: any) {
      logger.error('Preprocess inputs node failed', {
        session_id: state.session_id,
        error: error.message
      });

      return {
        error: `Input preprocessing failed: ${error.message}`,
        inputs_preprocessed: false
      };
    }
  }

  private async aggregateResultsNode(state: AgentDState): Promise<Partial<AgentDState>> {
    const perfLogger = new PerformanceLogger(`agent_d_aggregate_${state.session_id}`);

    try {
      logger.debug('Agent D aggregate results node', {
        session_id: state.session_id
      });

      // 构建最终提示词
      const prompt = await this.promptTemplate.format(state.processed_inputs);
      perfLogger.checkpoint('prompt_formatted');

      // 调用模型进行最终整合
      const response = await this.model.invoke(prompt);
      perfLogger.checkpoint('model_invoked');

      const aggregatedResult = this.processResponse(response.content);
      const tokens = this.estimateTokens(JSON.stringify(state.processed_inputs), aggregatedResult);
      const executionTime = perfLogger.end();

      return {
        aggregated_result: aggregatedResult,
        aggregation_completed: true,
        tokens_used: tokens,
        execution_time: executionTime,
        metadata: {
          input_agents_count: 3,
          output_length: aggregatedResult.length,
          model_used: this.config.model
        }
      };

    } catch (error: any) {
      logger.error('Aggregate results node failed', {
        session_id: state.session_id,
        error: error.message
      });

      return {
        error: `Aggregation failed: ${error.message}`,
        aggregation_completed: false
      };
    }
  }

  private sanitizeInput(input: any): string {
    if (typeof input === 'string') {
      return input.trim();
    }
    
    if (typeof input === 'object') {
      return JSON.stringify(input, null, 2);
    }
    
    return String(input || '').trim();
  }

  private async enhanceResponseNode(state: AgentDState): Promise<Partial<AgentDState>> {
    try {
      logger.debug('Agent D enhance response node', {
        session_id: state.session_id
      });

      const baseResponse = state.aggregated_result || '';

      // 添加执行摘要
      const executionSummary = this.generateExecutionSummary(state);

      // 添加质量指标
      const qualityMetrics = this.generateQualityMetrics(baseResponse, state);

      // 组合最终响应
      const enhancedResult = `${baseResponse}

${executionSummary}

${qualityMetrics}`;

      return {
        enhanced_result: enhancedResult,
        response_enhanced: true,
        execution_summary: executionSummary,
        quality_metrics: qualityMetrics
      };

    } catch (error: any) {
      logger.error('Enhance response node failed', {
        session_id: state.session_id,
        error: error.message
      });

      return {
        enhanced_result: state.aggregated_result || '',
        response_enhanced: false,
        error: `Response enhancement failed: ${error.message}`
      };
    }
  }

  private async finalizeResultNode(state: AgentDState): Promise<Partial<AgentDState>> {
    try {
      logger.debug('Agent D finalize result node', {
        session_id: state.session_id
      });

      const finalResult = state.enhanced_result || state.aggregated_result || '';

      return {
        final_result: finalResult
      };

    } catch (error: any) {
      logger.error('Finalize result node failed', {
        session_id: state.session_id,
        error: error.message
      });

      return {
        final_result: state.aggregated_result || '',
        error: `Finalize result failed: ${error.message}`
      };
    }
  }

  private processResponse(content: any): string {
    if (typeof content === 'string') {
      return content.trim();
    }
    
    if (content && typeof content === 'object') {
      return JSON.stringify(content);
    }
    
    return String(content || '').trim();
  }

  private generateExecutionSummary(state: AgentDState): string {
    const timestamp = new Date().toISOString();

    return `
=== 多智能体协作执行摘要 ===
• 执行时间: ${timestamp}
• 协作智能体: Agent A (深度分析) + Agent B (迭代优化) + Agent C (函数处理) + Agent D (结果整合)
• 原始输入长度: ${state.original_input?.length || 0} 字符
• Agent A 分析长度: ${state.agent_a_result?.length || 0} 字符
• Agent B 分析长度: ${state.agent_b_result?.length || 0} 字符
• Agent C 处理长度: ${state.agent_c_result?.length || 0} 字符
• 整合方式: 多维度信息融合与逻辑重构`;
  }

  private generateQualityMetrics(response: string, state: AgentDState): string {
    const wordCount = response.split(/\s+/).length;
    const hasStructure = response.includes('===') || response.includes('###') || response.includes('**');
    const referencesAllAgents = ['Agent A', 'Agent B', 'Agent C'].every(agent =>
      response.includes(agent) || response.includes(agent.toLowerCase())
    );

    return `
=== 回答质量指标 ===
• 回答长度: ${response.length} 字符 (约 ${wordCount} 词)
• 结构化程度: ${hasStructure ? '高' : '中等'}
• 信息整合度: ${referencesAllAgents ? '完整整合所有智能体结果' : '部分整合'}
• 响应完整性: ✓ 已整合多智能体分析结果
• 逻辑连贯性: ✓ 经过Agent D逻辑重构与优化
=== 协作完成 ===`;
  }

  private estimateTokens(input: string, output: string): number {
    const inputTokens = Math.ceil(input.length / 4);
    const outputTokens = Math.ceil(output.length / 4);
    return inputTokens + outputTokens;
  }

  // 健康检查方法
  async healthCheck(): Promise<boolean> {
    try {
      const testCombinedInput = {
        original_input: "健康检查测试",
        agent_a_result: "Agent A 分析结果",
        agent_b_result: "Agent B 迭代结果", 
        agent_c_result: "Agent C 函数结果"
      };
      
      const response = await this.model.invoke("请回复'Agent D健康检查通过'");
      return response && response.content !== null;
    } catch (error: any) {
      logger.warn('Agent D health check failed', { error: error.message });
      return false;
    }
  }

  // 获取配置信息
  getConfig(): any {
    return {
      name: this.config.name,
      description: this.config.description,
      model: this.config.model,
      temperature: this.config.temperature,
      maxTokens: this.config.maxTokens,
      role: 'aggregation',
      input_sources: ['agent_a', 'agent_b', 'agent_c'],
      output_type: 'final_response',
      implementation: 'StateGraph',
      workflow_nodes: ['validate_inputs', 'preprocess_inputs', 'aggregate_results', 'enhance_response', 'finalize_result']
    };
  }

  // 分析输入复杂度
  analyzeInputComplexity(combinedInput: any): any {
    try {
      const complexity = {
        total_input_length: 0,
        agent_contributions: {},
        estimated_processing_time: 0
      };

      // 计算各智能体贡献
      ['agent_a_result', 'agent_b_result', 'agent_c_result'].forEach(field => {
        const length = combinedInput[field]?.length || 0;
        complexity.total_input_length += length;
        (complexity.agent_contributions as any)[field] = {
          length,
          percentage: 0 // 稍后计算
        };
      });

      // 计算百分比
      Object.keys(complexity.agent_contributions).forEach(field => {
        const contrib = (complexity.agent_contributions as any)[field];
        contrib.percentage = complexity.total_input_length > 0 
          ? Math.round((contrib.length / complexity.total_input_length) * 100)
          : 0;
      });

      // 估算处理时间（基于输入复杂度）
      complexity.estimated_processing_time = Math.max(2000, complexity.total_input_length * 0.1);

      return complexity;
    } catch (error: any) {
      logger.warn('Input complexity analysis failed', { error: error.message });
      return { error: 'Analysis failed' };
    }
  }
}
