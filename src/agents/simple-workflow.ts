import { AgentState, NodeExe<PERSON><PERSON><PERSON><PERSON>ult, AgentError } from '../types';
import { logger, PerformanceLogger, AgentLogger } from '../utils/logger';
import { DatabaseConnection } from '../database/connection';
import { v4 as uuidv4 } from 'uuid';
import { StateGraph, Annotation, START, END } from '@langchain/langgraph';
import type { RunnableConfig } from '@langchain/core/runnables';

// 定义LangGraph状态注解
const WorkflowStateAnnotation = Annotation.Root({
  // 基本输入输出
  input: Annotation<string>,
  output: Annotation<string>({
    reducer: (left: string, right: string) => right || left,
    default: () => ''
  }),

  // 各节点执行结果
  nodeA_result: Annotation<string | undefined>({
    reducer: (left: string | undefined, right: string | undefined) => right !== undefined ? right : left,
    default: () => undefined
  }),
  nodeB_result: Annotation<string | undefined>({
    reducer: (left: string | undefined, right: string | undefined) => right !== undefined ? right : left,
    default: () => undefined
  }),
  nodeC_result: Annotation<string | undefined>({
    reducer: (left: string | undefined, right: string | undefined) => right !== undefined ? right : left,
    default: () => undefined
  }),
  nodeD_result: Annotation<string | undefined>({
    reducer: (left: string | undefined, right: string | undefined) => right !== undefined ? right : left,
    default: () => undefined
  }),

  // 会话和用户信息
  session_id: Annotation<string>,
  user_id: Annotation<string | undefined>({
    reducer: (left: string | undefined, right: string | undefined) => right !== undefined ? right : left,
    default: () => undefined
  }),

  // 迭代计数
  iteration_count: Annotation<number>({
    reducer: (left: number, right: number) => right,
    default: () => 0
  }),

  // 时间戳
  timestamp: Annotation<Date>({
    reducer: (left: Date, right: Date) => right,
    default: () => new Date()
  }),

  // 错误信息
  error: Annotation<string | undefined>({
    reducer: (left: string | undefined, right: string | undefined) => right !== undefined ? right : left,
    default: () => undefined
  }),

  // 元数据
  metadata: Annotation<Record<string, any> | undefined>({
    reducer: (left: Record<string, any> | undefined, right: Record<string, any> | undefined) => {
      if (right === undefined) return left;
      if (left === undefined) return right;
      return { ...left, ...right };
    },
    default: () => undefined
  })
});

// 定义状态类型
type WorkflowState = typeof WorkflowStateAnnotation.State;
type WorkflowUpdate = typeof WorkflowStateAnnotation.Update;

export class SimpleMultiAgentWorkflow {
  private db: DatabaseConnection;
  private agentA: any;
  private agentB: any;
  private agentC: any;
  private agentD: any;
  private initialized = false;
  private graph: any = null;

  constructor() {
    this.db = DatabaseConnection.getInstance();
  }

  private async initializeAgents() {
    if (this.initialized) return;
    
    try {
      const { AgentA } = await import('./agent-a');
      const { AgentB } = await import('./agent-b');
      const { AgentC } = await import('./agent-c');
      const { AgentD } = await import('./agent-d');
      
      this.agentA = new AgentA();
      this.agentB = new AgentB();
      this.agentC = new AgentC();
      this.agentD = new AgentD();
      
      this.initialized = true;
      logger.info('All agents initialized successfully');
    } catch (error: any) {
      logger.error('Failed to initialize agents', { error: error.message });
      throw new AgentError('Agent initialization failed', 'INIT_FAILED', 'workflow', { error: error.message });
    }
  }

  public async compile(): Promise<void> {
    try {
      await this.initializeAgents();

      this.graph = new StateGraph(WorkflowStateAnnotation)
        .addNode('start', this.createStartNode())
        .addNode('agentA', this.createAgentANode())
        .addNode('agentB', this.createAgentBNode())
        .addNode('agentC', this.createAgentCNode())
        .addNode('agentD', this.createAgentDNode())
        .addNode('end', this.createEndNode())
        .addEdge(START, 'start')
        .addEdge('start', 'agentA')
        .addEdge('start', 'agentB')
        .addEdge('agentA', 'agentC')
        .addEdge('agentB', 'agentD')
        .addEdge('agentC', 'agentD')
        .addEdge('agentD', 'end')
        .addEdge('end', END)
        .compile();

      logger.info('LangGraph workflow compiled successfully');
    } catch (error: any) {
      logger.error('Failed to compile workflow', { error: error.message });
      throw error;
    }
  }

  // 执行工作流（LangGraph版本）
  public async execute(input: string, userId?: string): Promise<AgentState> {
    const perfLogger = new PerformanceLogger('workflow_execution');
    const sessionId = uuidv4();

    try {
      if (!this.graph) {
        await this.compile();
      }

      logger.info('Starting LangGraph workflow execution', {
        session_id: sessionId,
        user_id: userId,
        input_length: input.length
      });

      // 初始状态
      const initialState: WorkflowState = {
        input,
        output: '',
        iteration_count: 0,
        session_id: sessionId,
        user_id: userId,
        timestamp: new Date(),
        nodeA_result: undefined,
        nodeB_result: undefined,
        nodeC_result: undefined,
        nodeD_result: undefined,
        error: undefined,
        metadata: undefined
      };

      // 使用LangGraph执行工作流
      const result = await this.graph.invoke(initialState);

      const executionTime = perfLogger.end({
        session_id: sessionId,
        success: !result.error,
        output_length: result.output?.length || 0
      });

      logger.info('LangGraph workflow execution completed', {
        session_id: sessionId,
        success: !result.error,
        execution_time: executionTime
      });

      // 转换为AgentState格式返回
      return {
        input: result.input,
        output: result.output,
        nodeA_result: result.nodeA_result,
        nodeB_result: result.nodeB_result,
        nodeC_result: result.nodeC_result,
        nodeD_result: result.nodeD_result,
        iteration_count: result.iteration_count,
        session_id: result.session_id,
        user_id: result.user_id,
        timestamp: result.timestamp,
        error: result.error,
        metadata: result.metadata
      };
    } catch (error: any) {
      perfLogger.end({ error: error.message });
      logger.error('LangGraph workflow execution failed', {
        session_id: sessionId,
        error: error.message,
        stack: error.stack
      });
      throw error;
    }
  }

  // 保存状态快照
  private async saveStateSnapshot(nodeId: string, sessionId: string, state: Partial<AgentState>): Promise<void> {
    try {
      const conversationsCollection = this.db.getConversationsCollection();
      await conversationsCollection.updateOne(
        { session_id: sessionId },
        {
          $push: {
            state_snapshots: {
              node_id: nodeId,
              state_data: state,
              timestamp: new Date(),
              checkpoint_id: uuidv4()
            }
          },
          $setOnInsert: {
            session_id: sessionId,
            created_at: new Date(),
            messages: []
          },
          $set: {
            updated_at: new Date()
          }
        },
        { upsert: true }
      );
    } catch (error: any) {
      logger.warn('Failed to save state snapshot', { nodeId, sessionId, error: error.message });
    }
  }

  // 保存智能体执行记录
  private async saveAgentExecution(
    nodeId: string, 
    sessionId: string, 
    input: any, 
    result: NodeExecutionResult
  ): Promise<void> {
    try {
      const executionsCollection = this.db.getAgentExecutionsCollection();
      await executionsCollection.insertOne({
        session_id: sessionId,
        node_id: nodeId,
        agent_type: nodeId.replace('agent_', ''),
        execution_start: new Date(),
        execution_end: new Date(),
        input_data: { input },
        output_data: result.success ? { result: result.result } : undefined,
        model_config: {
          model_name: process.env.OPENAI_MODEL_NAME || 'ht::saas-deepseek-v3',
          temperature: 0.7
        },
        performance_metrics: {
          latency_ms: result.execution_time,
          tokens_used: result.tokens_used || 0
        },
        error_info: result.error ? {
          error_type: 'EXECUTION_ERROR',
          error_message: result.error
        } : undefined
      });
    } catch (error: any) {
      logger.warn('Failed to save agent execution record', { nodeId, sessionId, error: error.message });
    }
  }

  // 保存最终结果
  private async saveFinalResult(state: AgentState): Promise<void> {
    try {
      const conversationsCollection = this.db.getConversationsCollection();
      await conversationsCollection.updateOne(
        { session_id: state.session_id },
        {
          $set: {
            final_result: {
              success: !state.error,
              output: state.output || '',
              error_message: state.error,
              total_processing_time: state.metadata?.total_execution_time || 0
            },
            updated_at: new Date()
          }
        }
      );
    } catch (error: any) {
      logger.warn('Failed to save final result', { sessionId: state.session_id, error: error.message });
    }
  }

  // ========== LangGraph 节点函数 ==========

  // 启动节点 - LangGraph版本
  private createStartNode() {
    return async (state: WorkflowState): Promise<WorkflowUpdate> => {
      const perfLogger = new PerformanceLogger('start_node');

      try {
        // 生成session_id如果不存在
        const sessionId = state.session_id || uuidv4();

        AgentLogger.logExecution('start', sessionId, 'start', {
          input: state.input,
          user_id: state.user_id
        });

        // 初始化状态
        const updatedState: Partial<typeof WorkflowStateAnnotation.State> = {
          session_id: sessionId,
          timestamp: new Date(),
          iteration_count: 0,
          metadata: {
            workflow_version: '1.0',
            start_time: new Date().toISOString()
          }
        };

        // 保存初始状态到数据库
        await this.saveStateSnapshot('start', sessionId, updatedState);

        perfLogger.end();
        AgentLogger.logExecution('start', sessionId, 'end');

        return updatedState;
      } catch (error: any) {
        perfLogger.end({ error: error.message });
        AgentLogger.logExecution('start', state.session_id, 'error', { error: error.message });
        throw new AgentError('Start node failed', 'START_FAILED', 'start', { error });
      }
    };
  }

  // Agent A 节点 - LangGraph版本
  private createAgentANode() {
    return async (state: typeof WorkflowStateAnnotation.State): Promise<Partial<typeof WorkflowStateAnnotation.State>> => {
      const perfLogger = new PerformanceLogger('agent_a');

      try {
        AgentLogger.logExecution('agent_a', state.session_id, 'start', {
          input: state.input
        });

        const result = await this.agentA.execute(state.input, state.session_id);

        const updatedState: Partial<typeof WorkflowStateAnnotation.State> = {
          nodeA_result: result.result,
          metadata: {
            ...state.metadata,
            agent_a_execution_time: result.execution_time,
            agent_a_tokens_used: result.tokens_used
          }
        };

        // 保存执行结果
        await this.saveAgentExecution('agent_a', state.session_id, state.input, result);
        await this.saveStateSnapshot('agent_a', state.session_id, updatedState);

        perfLogger.end({ tokens_used: result.tokens_used });
        AgentLogger.logExecution('agent_a', state.session_id, 'end', {
          result_length: result.result?.length || 0
        });

        return updatedState;
      } catch (error: any) {
        perfLogger.end({ error: error.message });
        AgentLogger.logExecution('agent_a', state.session_id, 'error', { error: error.message });

        return {
          error: `Agent A failed: ${error.message}`,
          nodeA_result: `[ERROR] Agent A 执行失败: ${error.message}`,
          metadata: {
            ...state.metadata,
            agent_a_execution_time: 0,
            agent_a_tokens_used: 0,
            agent_a_error: error.message
          }
        };
      }
    };
  }

  // Agent B 节点 - LangGraph版本
  private createAgentBNode() {
    return async (state: typeof WorkflowStateAnnotation.State): Promise<Partial<typeof WorkflowStateAnnotation.State>> => {
      const perfLogger = new PerformanceLogger('agent_b');

      try {
        AgentLogger.logExecution('agent_b', state.session_id, 'start', {
          input: state.input,
          iteration: state.iteration_count
        });

        const result = await this.agentB.execute(state.input, state.session_id, state.iteration_count);

        const updatedState: Partial<typeof WorkflowStateAnnotation.State> = {
          nodeB_result: result.result,
          iteration_count: state.iteration_count + 1,
          metadata: {
            ...state.metadata,
            agent_b_execution_time: result.execution_time,
            agent_b_tokens_used: result.tokens_used,
            agent_b_iterations: state.iteration_count + 1
          }
        };

        // 保存执行结果
        await this.saveAgentExecution('agent_b', state.session_id, state.input, result);
        await this.saveStateSnapshot('agent_b', state.session_id, updatedState);

        perfLogger.end({
          tokens_used: result.tokens_used,
          iteration: state.iteration_count + 1
        });
        AgentLogger.logExecution('agent_b', state.session_id, 'end', {
          result_length: result.result?.length || 0,
          iteration: state.iteration_count + 1
        });

        return updatedState;
      } catch (error: any) {
        perfLogger.end({ error: error.message });
        AgentLogger.logExecution('agent_b', state.session_id, 'error', { error: error.message });

        return {
          error: `Agent B failed: ${error.message}`,
          nodeB_result: `[ERROR] Agent B 执行失败: ${error.message}`,
          iteration_count: state.iteration_count,
          metadata: {
            ...state.metadata,
            agent_b_execution_time: 0,
            agent_b_tokens_used: 0,
            agent_b_iterations: 0,
            agent_b_error: error.message
          }
        };
      }
    };
  }

  // Agent C 节点 - LangGraph版本
  private createAgentCNode() {
    return async (state: typeof WorkflowStateAnnotation.State): Promise<Partial<typeof WorkflowStateAnnotation.State>> => {
      const perfLogger = new PerformanceLogger('agent_c');

      try {
        AgentLogger.logExecution('agent_c', state.session_id, 'start', {
          agent_a_result: state.nodeA_result
        });

        // Agent C 需要等待 Agent A 的结果
        if (!state.nodeA_result) {
          throw new AgentError('Agent C requires Agent A result', 'MISSING_DEPENDENCY', 'agent_c');
        }

        const result = await this.agentC.execute(state.nodeA_result, state.session_id);

        const updatedState: Partial<typeof WorkflowStateAnnotation.State> = {
          nodeC_result: result.result,
          metadata: {
            ...state.metadata,
            agent_c_execution_time: result.execution_time
          }
        };

        // 保存执行结果
        await this.saveAgentExecution('agent_c', state.session_id, state.nodeA_result, result);
        await this.saveStateSnapshot('agent_c', state.session_id, updatedState);

        perfLogger.end();
        AgentLogger.logExecution('agent_c', state.session_id, 'end', {
          result: result.result
        });

        return updatedState;
      } catch (error: any) {
        perfLogger.end({ error: error.message });
        AgentLogger.logExecution('agent_c', state.session_id, 'error', { error: error.message });

        return {
          error: `Agent C failed: ${error.message}`,
          nodeC_result: `[ERROR] Agent C 执行失败: ${error.message}`,
          metadata: {
            ...state.metadata,
            agent_c_execution_time: 0,
            agent_c_error: error.message
          }
        };
      }
    };
  }

  // Agent D 节点 - LangGraph版本
  private createAgentDNode() {
    return async (state: typeof WorkflowStateAnnotation.State): Promise<Partial<typeof WorkflowStateAnnotation.State>> => {
      const perfLogger = new PerformanceLogger('agent_d');

      try {
        AgentLogger.logExecution('agent_d', state.session_id, 'start', {
          has_agent_b_result: !!state.nodeB_result,
          has_agent_c_result: !!state.nodeC_result
        });

        // Agent D 需要等待 Agent B 和 Agent C 的结果
        if (!state.nodeB_result || !state.nodeC_result) {
          logger.warn('Agent D missing dependencies', {
            session_id: state.session_id,
            has_nodeB_result: !!state.nodeB_result,
            has_nodeC_result: !!state.nodeC_result,
            nodeB_result_type: typeof state.nodeB_result,
            nodeC_result_type: typeof state.nodeC_result
          });
          throw new AgentError('Agent D requires both Agent B and Agent C results', 'MISSING_DEPENDENCIES', 'agent_d');
        }

        // 检查是否有错误状态，如果有则进行降级处理
        const hasErrors = state.error ||
                         (typeof state.nodeB_result === 'string' && state.nodeB_result.startsWith('[ERROR]')) ||
                         (typeof state.nodeC_result === 'string' && state.nodeC_result.startsWith('[ERROR]'));

        if (hasErrors) {
          logger.info('Agent D executing in degraded mode due to upstream errors', {
            session_id: state.session_id,
            upstream_error: state.error
          });
        }

        const combinedInput = {
          original_input: state.input,
          agent_a_result: state.nodeA_result,
          agent_b_result: state.nodeB_result,
          agent_c_result: state.nodeC_result
        };

        const result = await this.agentD.execute(combinedInput, state.session_id);

        const updatedState: Partial<typeof WorkflowStateAnnotation.State> = {
          nodeD_result: result.result,
          output: result.result, // 设置最终输出
          metadata: {
            ...state.metadata,
            agent_d_execution_time: result.execution_time,
            agent_d_tokens_used: result.tokens_used,
            total_execution_time: Date.now() - new Date(state.timestamp).getTime()
          }
        };

        // 保存执行结果
        await this.saveAgentExecution('agent_d', state.session_id, combinedInput, result);
        await this.saveStateSnapshot('agent_d', state.session_id, updatedState);

        perfLogger.end({ tokens_used: result.tokens_used });
        AgentLogger.logExecution('agent_d', state.session_id, 'end', {
          result_length: result.result?.length || 0
        });

        return updatedState;
      } catch (error: any) {
        perfLogger.end({ error: error.message });
        AgentLogger.logExecution('agent_d', state.session_id, 'error', { error: error.message });

        return {
          error: `Agent D failed: ${error.message}`,
          nodeD_result: undefined,
          output: `工作流执行失败: ${error.message}`
        };
      }
    };
  }

  // 结束节点 - LangGraph版本
  private createEndNode() {
    return async (state: typeof WorkflowStateAnnotation.State): Promise<Partial<typeof WorkflowStateAnnotation.State>> => {
      const perfLogger = new PerformanceLogger('end_node');

      try {
        AgentLogger.logExecution('end', state.session_id, 'start');

        // 保存最终结果到数据库
        await this.saveFinalResult(state as AgentState);

        perfLogger.end();
        AgentLogger.logExecution('end', state.session_id, 'end', {
          success: !state.error,
          output_length: state.output?.length || 0
        });

        return {
          timestamp: new Date(),
          metadata: {
            ...state.metadata,
            end_time: new Date().toISOString(),
            workflow_completed: true
          }
        };
      } catch (error: any) {
        perfLogger.end({ error: error.message });
        AgentLogger.logExecution('end', state.session_id, 'error', { error: error.message });
        throw new AgentError('End node failed', 'END_FAILED', 'end', { error });
      }
    };
  }
}
